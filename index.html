<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Interface</title>
    <link rel="stylesheet" href="style.css">
    <!-- Add highlight.js for syntax highlighting -->
    <link rel="stylesheet" href="highlight-light.css" id="highlight-light-theme">
    <link rel="stylesheet" href="highlight-dark.css" id="highlight-dark-theme" disabled>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/html.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/json.min.js"></script>
    <!-- Add CodeMirror for code editor -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/theme/material-darker.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/theme/default.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/python/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/htmlmixed/htmlmixed.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/css/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/xml/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/hint/show-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/addon/hint/javascript-hint.min.js"></script>
    <!-- Add Font Awesome for modern icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="dark-theme">
    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="app-logo">
                    <img src="icons/ai_chat_logo.svg" alt="AI Chat" class="logo-icon">
                    <span>AI Chat</span>
                </div>
            </div>
            <button id="new-chat-button" class="new-chat-button">
                + New Chat
            </button>
            <div class="history-section">
                <div class="history-header">
                    <span>History</span>
                    <button id="export-import-toggle" class="history-action-button" title="Export/Import Chat History">
                        <i class="fa-solid fa-download"></i>
                    </button>
                </div>
                <div id="chat-history" class="chat-history"></div>
            </div>
        </div>
        <div class="chat-container">
            <div class="chat-window">
                <div class="chat-header">
                    <div class="header-actions">
                        <button id="theme-toggle" class="icon-button" title="Toggle dark/light mode">
                            <img src="icons/light_mode.svg" alt="Theme" class="icon">
                        </button>
                        <button id="code-editor-toggle" class="icon-button" title="Toggle Code Editor">
                            <img src="icons/code.svg" alt="Code" class="icon">
                        </button>
                        <button id="ai-persona-toggle" class="icon-button" title="AI Persona Settings">
                            <i class="fa-solid fa-user-gear"></i>
                        </button>
                        <button id="mobile-sidebar-toggle" class="mobile-sidebar-toggle">
                            <i class="fa-solid fa-bars"></i>
                        </button>
                    </div>
                </div>
                <div class="main-content">
                    <div id="chat-display" class="chat-display"></div>
                    <div id="chat-editor-resize" class="chat-editor-resize"></div>
                    <div id="code-editor-panel" class="code-editor-panel" style="display: none;">
                        <div class="code-editor-header">
                            <div class="code-editor-controls">
                                <select id="language-select" class="language-select">
                                    <option value="javascript">JavaScript</option>
                                    <option value="python">Python</option>
                                    <option value="htmlmixed">HTML</option>
                                    <option value="css">CSS</option>
                                    <option value="xml">XML</option>
                                    <option value="htmlcss">HTML+CSS</option>
                                </select>
                                <button id="run-code-button" class="run-code-button" title="Run Code">
                                    <i class="fa-solid fa-play"></i> Run
                                </button>
                                <button id="share-to-chat-button" class="share-to-chat-button" title="Share to Chat">
                                    <i class="fa-solid fa-share"></i> Share to Chat
                                </button>
                                <button id="fullscreen-preview-button" class="fullscreen-preview-button" title="Fullscreen Preview">
                                    <i class="fa-solid fa-expand"></i>
                                </button>
                                <button id="copy-code-button" class="copy-code-button" title="Copy Code">
                                    <i class="fa-regular fa-copy"></i> Copy
                                </button>
                                <button id="download-code-button" class="download-code-button" title="Download Code">
                                    <i class="fa-solid fa-download"></i> Download
                                </button>
                            </div>
                            <div class="editor-tab-controls">
                                <button id="code-tab-button" class="editor-tab-button active" title="Code View">
                                    <i class="fa-solid fa-code"></i> Code
                                </button>
                                <button id="preview-tab-button" class="editor-tab-button" title="Preview">
                                    <i class="fa-solid fa-eye"></i> Preview
                                </button>
                                <button id="close-editor-button" class="close-editor-button" title="Close Editor">
                                    <i class="fa-solid fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="editor-content">
                            <div id="editor-container" class="editor-container">
                                <textarea id="code-editor"></textarea>
                            </div>
                            <div id="resize-handle" class="resize-handle"></div>
                            <div id="preview-container" class="preview-container">
                                <div id="code-output" class="code-output">
                                    <div class="output-header">
                                        <span>Output</span>
                                        <div class="output-controls">
                                            <button id="clear-output-button" class="clear-output-button" title="Clear Output">
                                                <i class="fa-solid fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="output-content-wrapper" class="output-content-wrapper">
                                        <pre id="output-content"></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Persona Settings Panel -->
                <div id="ai-persona-panel" class="settings-panel">
                    <div class="settings-header">
                        <h3>AI Persona Settings</h3>
                        <button id="close-persona-panel" class="close-panel-button">
                            <i class="fa-solid fa-times"></i>
                        </button>
                    </div>
                    <div class="settings-content">
                        <div class="setting-group">
                            <label for="persona-select">AI Persona:</label>
                            <select id="persona-select" class="setting-select">
                                <option value="expert-engineer">Expert Software Engineer</option>
                                <option value="code-reviewer">Code Reviewer</option>
                                <option value="architect">Software Architect</option>
                                <option value="mentor">Programming Mentor</option>
                                <option value="custom">Custom</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label for="expertise-areas">Expertise Areas:</label>
                            <div class="checkbox-group">
                                <label><input type="checkbox" value="javascript" checked> JavaScript</label>
                                <label><input type="checkbox" value="python" checked> Python</label>
                                <label><input type="checkbox" value="react" checked> React</label>
                                <label><input type="checkbox" value="nodejs" checked> Node.js</label>
                                <label><input type="checkbox" value="databases" checked> Databases</label>
                                <label><input type="checkbox" value="devops" checked> DevOps</label>
                            </div>
                        </div>
                        <div class="setting-group">
                            <label for="response-style">Response Style:</label>
                            <select id="response-style" class="setting-select">
                                <option value="detailed">Detailed Explanations</option>
                                <option value="concise">Concise & Direct</option>
                                <option value="step-by-step">Step-by-Step</option>
                                <option value="examples">Example-Heavy</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label for="custom-prompt">Custom System Prompt:</label>
                            <textarea id="custom-prompt" class="setting-textarea" rows="4" placeholder="Enter custom system prompt..."></textarea>
                        </div>
                        <div class="setting-actions">
                            <button id="save-persona" class="action-button primary">Save Settings</button>
                            <button id="reset-persona" class="action-button secondary">Reset to Default</button>
                        </div>
                    </div>
                </div>

                <!-- Export/Import Panel -->
                <div id="export-import-panel" class="settings-panel">
                    <div class="settings-header">
                        <h3>Export/Import Chat History</h3>
                        <button id="close-export-panel" class="close-panel-button">
                            <i class="fa-solid fa-times"></i>
                        </button>
                    </div>
                    <div class="settings-content">
                        <div class="export-section">
                            <h4>Export Chats</h4>
                            <div class="setting-group">
                                <label for="export-format">Export Format:</label>
                                <select id="export-format" class="setting-select">
                                    <option value="json">JSON (Complete Data)</option>
                                    <option value="markdown">Markdown (Readable)</option>
                                    <option value="text">Plain Text</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label for="export-range">Date Range:</label>
                                <div class="date-range">
                                    <input type="date" id="export-start-date" class="date-input">
                                    <span>to</span>
                                    <input type="date" id="export-end-date" class="date-input">
                                </div>
                            </div>
                            <div class="setting-group">
                                <label>Select Chats to Export:</label>
                                <div id="chat-selection" class="chat-selection"></div>
                            </div>
                            <button id="export-selected" class="action-button primary">Export Selected</button>
                            <button id="export-all" class="action-button secondary">Export All</button>
                        </div>
                        <div class="import-section">
                            <h4>Import Chats</h4>
                            <div class="setting-group">
                                <label for="import-file">Select File:</label>
                                <input type="file" id="import-file" accept=".json,.md,.txt" class="file-input">
                            </div>
                            <button id="import-chats" class="action-button primary">Import Chats</button>
                        </div>
                    </div>
                </div>

                <!-- Code Integration Panel -->
                <div id="code-integration-panel" class="overlay-panel" style="display: none;">
                    <div class="panel-content">
                        <div class="panel-header">
                            <h3>Apply Code Changes</h3>
                            <button id="close-code-panel" class="close-panel-button">
                                <i class="fa-solid fa-times"></i>
                            </button>
                        </div>
                        <div class="code-preview">
                            <div class="file-tabs" id="file-tabs"></div>
                            <div class="diff-viewer" id="diff-viewer"></div>
                        </div>
                        <div class="panel-actions">
                            <button id="preview-changes" class="action-button secondary">Preview Changes</button>
                            <button id="apply-changes" class="action-button primary">Apply Changes</button>
                            <button id="cancel-changes" class="action-button">Cancel</button>
                        </div>
                    </div>
                </div>

                <div class="input-container">
                    <div id="file-preview" class="file-preview"></div>
                    <div class="input-area">
                        <button id="attach-button" class="input-icon-button" title="Attach file">
                            <img src="icons/attachment_icon.svg" alt="Attach" class="input-icon">
                        </button>
                        <select id="model-select" class="model-select" title="Select AI model">
                            <option value="">Select a model...</option>
                        </select>
                        <input type="file" id="file-input" accept=".pdf,.txt,.cs,.py,.js,.java,.cpp,.c,.h,.html,.css,.php,.rb,.go,.rs,.sh,.md,.json,.xml,.yaml,.yml,.jpg,.jpeg,.png,.gif,.webp" style="display: none;" title="Upload documents, code files, or images for analysis">
                        <div class="message-input-container">
                            <input type="text" id="message-input" placeholder="Ask me anything...">
                        </div>
                        <button id="send-button" class="input-icon-button send-button">
                            <img src="icons/send_icon.svg" alt="Send" class="input-icon">
                        </button>
                        <button id="apply-code-button" class="input-icon-button apply-code-button" style="display: none;" title="Apply AI code suggestions">
                            <i class="fa-solid fa-magic-wand-sparkles"></i>
                        </button>
                        <button id="stop-button" class="action-button stop-button" style="display: none;">
                            <i class="fa-solid fa-stop"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
    <script src="marked.min.js"></script>
    <script src="ai-persona.js"></script>
    <script src="code-integration.js"></script>
    <script src="export-import.js"></script>
    <script src="script.js"></script>
    <script src="fix-code-buttons.js"></script>
</body>
</html>
